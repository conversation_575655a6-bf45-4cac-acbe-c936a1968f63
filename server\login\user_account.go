package login

import (
	"strings"

	g "casrv/server/common"
	"casrv/server/common/ecode"
	ut "casrv/utils"
	rds "casrv/utils/redis"
	"casrv/utils/sensitive"

	"github.com/huyangv/vmqant/log"
)

// 创建账号
func createUserAccount(loginType, guestId, platform, nickname string, roleId int32, ip string) (err string, uid string) {
	uid = genUID()
	if loginType == g.LOGIN_TYPE_GUEST {
		nickname = nickname + uid
	} else if nickname == "" || !checkNickname(nickname) {
		nickname = "User" + uid
	} else {
		nickname = ut.TruncateString(strings.Trim(nickname, " "), 14)
	}
	data := g.NewUserTableData(uid, loginType, guestId, nickname, roleId, platform)
	if e := db.InsertOne(data); e != "" {
		log.Error("createUserAccount err: %v", e)
		err = ecode.DB_ERROR.String()
	} else {
		log.Info("createUserAccount uid: %v, nickname: %v, roleId: %v, loginType: %v, platform: %v, ip: %v", uid, nickname, roleId, loginType, platform, ip)
	}
	return
}

// 获取登录游戏的token
func getGameToken(uid string) (accountToken string) {
	// 先更新登录时间
	time := ut.Now()
	// 重新生成token
	str := uid + ut.Itoa(time)
	accountToken = ut.AESEncrypt(str)
	rds.RdsHSet(uid, rds.RDS_USER_FIELD_TOKEN, accountToken)
	return
}

// 生成唯一id
func genUID() string {
	uid := ut.UID8()
	for db.HasUid(uid) {
		uid = ut.UID8()
		log.Info("生成uid的时候发现一样的 uid: %v", uid)
	}
	return uid
}

// 检测名字是否存在和合法
func checkNickname(nickname string) bool {
	if sensitive.CheckName(nickname) != 0 {
		return false // 是否合法
	}
	return !db.HasNickname(nickname)
}

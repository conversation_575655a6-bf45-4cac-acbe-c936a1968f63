package lobby

import (
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"

	"github.com/huyangv/vmqant/log"
)

const (
	BEGIN_GAME_SHOP_ID = 1 //开始游戏的商店id
)

type Item struct {
	Id int32 `json:"id"`
	Lv int8  `json:"lv"`
}

type Animal struct {
	Item
}

func (this *Item) ToPb() *pb.Item     { return &pb.Item{Id: this.Id, Lv: int32(this.Lv)} }
func (this *Animal) ToPb() *pb.Animal { return &pb.Animal{Id: this.Id, Lv: int32(this.Lv)} }

// 商店信息
type ShopInfo struct {
	Id    int32   `json:"id"`    //商店id
	Items []*Item `json:"items"` //物品列表
}

func (this *ShopInfo) ToPb() *pb.ShopInfo {
	return &pb.ShopInfo{Id: this.Id, Items: array.Map(this.Items, func(m *Item, _ int) *pb.Item { return m.ToPb() })}
}

// 其他玩家的信息
type PlayerInfo struct {
	Animals  []*Animal `json:"animals"` //动物列表
	Bags     []*Item   `json:"bags"`    //背包
	UID      string    `json:"uid"`
	Nickname string    `json:"nickname"`
	RoleId   int32     `json:"roleId"` //角色id
	Day      int16     `json:"day"`    //天数
}

func (this *PlayerInfo) ToPb() *pb.PlayerInfo {
	return &pb.PlayerInfo{
		Animals:  array.Map(this.Animals, func(m *Animal, _ int) *pb.Animal { return m.ToPb() }),
		Bags:     array.Map(this.Bags, func(m *Item, _ int) *pb.Item { return m.ToPb() }),
		Uid:      this.UID,
		Nickname: this.Nickname,
		RoleId:   this.RoleId,
		Day:      int32(this.Day),
	}
}

// 游戏数据
type GameData struct {
	Animals     []*Animal   `json:"animals"`     //动物列表
	Bags        []*Item     `json:"bags"`        //背包
	Shop        *ShopInfo   `json:"shopInfo"`    // 商店信息
	OtherPlayer *PlayerInfo `json:"otherPlayer"` //其他玩家信息
	CreateTime  int64       `json:"createTime"`  //创建时间
	Day         int16       `json:"day"`         //天数
}

func (this *GameData) ToPb() *pb.GameData {
	return &pb.GameData{
		Animals:     array.Map(this.Animals, func(m *Animal, _ int) *pb.Animal { return m.ToPb() }),
		Bags:        array.Map(this.Bags, func(m *Item, _ int) *pb.Item { return m.ToPb() }),
		Shop:        this.Shop.ToPb(),
		OtherPlayer: this.OtherPlayer.ToPb(),
		Day:         int32(this.Day),
	}
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "data")
	if err != nil || jsonStr == "" {
		return nil
	}
	// 将JSON字符串反序列化为GameData对象
	var gameData GameData
	if err := json.Unmarshal([]byte(jsonStr), &gameData); err != nil {
		log.Error("GetGameData unmarshal error: %v", err)
		return nil
	}
	return &gameData
}

// 保存游戏数据到redis
func SaveGameData(uid string, data *GameData) error {
	// 将GameData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveGameData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "data", string(jsonBytes))
}

// 创建游戏信息
func CreateGame(user *User) *GameData {
	// 先尝试从redis获取已有的游戏数据
	data := GetGameData(user.UID)
	if data != nil {
		return data
	}
	// 如果没有已有数据，创建新的游戏数据
	data = &GameData{
		CreateTime: ut.Now(),
		Day:        1, //从第一天开始
		Animals:    []*Animal{},
		Bags:       []*Item{},
	}
	// 随机三个东西 变异幼年动物 金币和收益 祝福
	data.Shop = &ShopInfo{
		Id: BEGIN_GAME_SHOP_ID,
	}
	// 保存到redis
	SaveGameData(user.UID, data)
	return data
}

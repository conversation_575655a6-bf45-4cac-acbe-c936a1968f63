package lobby

import (
	"casrv/server/common/ecode"
	"casrv/server/common/pb"

	"github.com/huyangv/vmqant/gate"
)

func (this *Lobby) initHDGame() {
	this.GetServer().RegisterGO("HD_GameBegin", this.gameBegin) // 创建游戏信息
}

// 游戏开始
func (this *Lobby) gameBegin(session gate.Session, msg *pb.LOBBY_HD_GAMEBEGIN_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	gameData := CreateGame(user)
	return pb.ProtoMarshal(&pb.LOBBY_HD_GAMEBEGIN_S2C{GameData: gameData.ToPb()})
}

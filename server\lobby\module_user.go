package lobby

import (
	g "casrv/server/common"
	"casrv/server/common/ecode"
	"casrv/server/common/pb"
	ut "casrv/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) initHDUser() {
	this.GetServer().RegisterGO("HD_TryLogin", this.tryLogin) // 尝试登录
}

// 更新使用语言
func (this *Lobby) updateUserLanguage(user *User, lang string) {
	oldLang := user.Language
	if oldLang != lang {
		user.Language = lang
	}
}

func (this *Lobby) BindUserAndSend(session gate.Session, user *User) (string, string) {
	log.Info("1 BindUser uid: %v, newUser: %v", user.UID, user.LastLoginTime == 0)
	if !isRunning {
		return "", ecode.ROOM_CLOSE.String() // 服务器已关闭
	}
	// 直接绑定uid
	session.Bind(user.UID)
	user.Session = session
	// 记录登录天数
	now := ut.Now()
	t1 := ut.DateZeroTime(user.LastLoginTime) // 最后一次登录时间
	t2 := ut.DateZeroTime(now)                // 当前登录时间
	day := (t2 - t1) / ut.TIME_DAY
	if day > 0 {
		user.LoginDayCount += 1
		// 连续登录天数
		if day == 1 {
			user.ContinueLoginDays += 1
		} else {
			user.ContinueLoginDays = 1
		}
	}
	// 生成sessionId
	user.SessionId = user.UID + ut.String(now)
	log.Info("2 BindUser uid: %v, done.", user.UID)
	// 返回
	return GetGameToken(user, session.GetIP()), ""
}

func (this *Lobby) tryLogin(session gate.Session, msg *pb.LOBBY_HD_TRYLOGIN_C2S) (bytes []byte, err string) {
	if msg.GetVersion() != g.CLIENT_VERSION {
		return nil, ecode.VERSION_TOOLOW.String() // 版本号不对
	}
	accountToken := msg.GetAccountToken()
	user, err := DecodeToken(accountToken, this) // 解析token并获取user
	if err != "" {
		return nil, err
	}
	// 检测封禁
	banAccountEndTime := ut.MaxInt64(0, user.BanAccountEndTime-ut.Now())
	if user.BanAccountType != -1 && banAccountEndTime > 0 {
		body, _ := pb.ProtoMarshal(&pb.LOBBY_HD_TRYLOGIN_S2C{BanAccountType: user.BanAccountType, BanAccountSurplusTime: banAccountEndTime})
		user.SetOffline()
		return body, ecode.BAN_ACCOUNT.String()
	}
	accountToken, err = this.BindUserAndSend(session, user)
	if err != "" {
		user.SetOffline()
		return nil, err
	}
	// 更像平台
	if platform := msg.GetPlatform(); platform != "" {
		user.Platform = platform
	}
	// 更新语言
	this.updateUserLanguage(user, msg.GetLang())
	user.FlagUpdateDB()
	// 获取游戏信息
	var gameDataPb *pb.GameData = nil
	if gameData := GetGameData(user.UID); gameData != nil {
		gameDataPb = gameData.ToPb()
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_TRYLOGIN_S2C{User: user.ToPb(), AccountToken: accountToken, inGame: gameDataPb})
}
